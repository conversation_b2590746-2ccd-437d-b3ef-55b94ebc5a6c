<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkillVerdict | Monetize Your Expertise as a Developer</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary: #f59e0b;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --success: #10b981;
            --vetter-bg: #eff6ff;
            --candidate-bg: #f0fdf4;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            color: var(--dark);
            background-color: var(--light);
            line-height: 1.6;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header & Navigation */
        header {
            background-color: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 100;
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            display: flex;
            align-items: center;
        }
        
        .logo i {
            margin-right: 8px;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
        }
        
        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: var(--primary);
        }
        
        .cta-button {
            background-color: var(--primary);
            color: white;
            padding: 10px 20px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: background-color 0.3s;
        }
        
        .cta-button:hover {
            background-color: var(--primary-dark);
        }
        
        /* Hero Section */
        .hero {
            padding: 150px 0 100px;
            text-align: center;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            line-height: 1.2;
        }
        
        .hero p {
            font-size: 1.5rem;
            color: var(--gray);
            max-width: 800px;
            margin: 0 auto 40px;
        }
        
        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .hero-btn {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background-color: white;
            color: var(--primary);
            border: 2px solid var(--primary);
        }
        
        .btn-secondary:hover {
            background-color: var(--primary);
            color: white;
            transform: translateY(-2px);
        }
        
        .email-form {
            display: flex;
            max-width: 500px;
            margin: 40px auto 0;
        }
        
        .email-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #ddd;
            border-radius: 50px 0 0 50px;
            font-size: 1rem;
            outline: none;
        }
        
        .submit-button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 0 50px 50px 0;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-button:hover {
            background-color: var(--primary-dark);
        }
        
        /* Sections */
        section {
            padding: 80px 0;
        }
        
        section h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 50px;
            color: var(--dark);
        }
        
        /* Two Sides Section */
        .two-sides {
            background-color: white;
        }
        
        .sides-container {
            display: flex;
            gap: 40px;
            flex-wrap: wrap;
        }
        
        .side-card {
            flex: 1;
            min-width: 300px;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .vetter-side {
            background-color: var(--vetter-bg);
            border-top: 5px solid var(--primary);
        }
        
        .candidate-side {
            background-color: var(--candidate-bg);
            border-top: 5px solid var(--success);
        }
        
        .side-card h3 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .benefit-list {
            list-style-type: none;
        }
        
        .benefit-list li {
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
        }
        
        .benefit-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--success);
            font-weight: bold;
        }
        
        /* For Vetters Section */
        .for-vetters {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }
        
        .vetter-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .vetter-card {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            text-align: center;
        }
        
        .vetter-card i {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 20px;
        }
        
        .vetter-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--primary);
        }
        
        /* How It Works */
        .process {
            background-color: white;
        }
        
        .process-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            border-bottom: 1px solid #ddd;
        }
        
        .process-tab {
            padding: 15px 30px;
            cursor: pointer;
            font-weight: 600;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .process-tab.active {
            border-bottom: 3px solid var(--primary);
            color: var(--primary);
        }
        
        .process-content {
            display: none;
        }
        
        .process-content.active {
            display: block;
        }
        
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .step {
            text-align: center;
            padding: 30px 20px;
        }
        
        .step .icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--primary);
        }
        
        .step h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        /* Earnings Section */
        .earnings {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            text-align: center;
        }
        
        .earning-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .earning-card {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }
        
        .earning-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--primary);
        }
        
        .earning-amount {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--success);
            margin: 15px 0;
        }
        
        /* Testimonial */
        .testimonial {
            background-color: white;
        }
        
        .testimonials-container {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .testimonial-card {
            flex: 1;
            min-width: 300px;
            background-color: var(--light);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }
        
        .testimonial-text {
            font-style: italic;
            margin-bottom: 20px;
            position: relative;
            padding: 0 20px;
        }
        
        .testimonial-text:before,
        .testimonial-text:after {
            content: """;
            font-size: 3rem;
            color: var(--primary);
            opacity: 0.2;
            position: absolute;
        }
        
        .testimonial-text:before {
            top: -20px;
            left: 0;
        }
        
        .testimonial-text:after {
            content: """;
            bottom: -40px;
            right: 0;
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .author-info h4 {
            margin-bottom: 5px;
        }
        
        .author-info p {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        /* Final CTA */
        .final-cta {
            text-align: center;
            padding: 80px 0;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }
        
        .final-cta h2 {
            margin-bottom: 20px;
        }
        
        .final-cta p {
            font-size: 1.2rem;
            color: var(--gray);
            max-width: 600px;
            margin: 0 auto 40px;
        }
        
        /* Footer */
        footer {
            background-color: var(--dark);
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        footer a {
            color: #93c5fd;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        footer a:hover {
            color: white;
        }
        
        .social-links {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .social-links a {
            color: white;
            font-size: 1.5rem;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.2rem;
            }
            
            .email-form {
                flex-direction: column;
            }
            
            .email-input {
                border-radius: 50px;
                margin-bottom: 10px;
            }
            
            .submit-button {
                border-radius: 50px;
            }
            
            .nav-links {
                display: none;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .hero-btn {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header & Navigation -->
    <header>
        <div class="container">
            <nav>
                <div class="logo">
                    <i class="fas fa-certificate"></i>
                    SkillVerdict
                </div>
                <div class="nav-links">
                    <a href="#home">Home</a>
                    <a href="#for-vetters">For Vetters</a>
                    <a href="#for-candidates">For Candidates</a>
                    <a href="#how-it-works">How It Works</a>
                    <a href="#waitlist" class="cta-button">Join Waitlist</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <h1>What if your skills on Linkedin could be verified by world's best developers?</h1>
            <p>SkillVerdict is a decentralized community connects that connects
            world's best senior developers with those seeking to
            verify their skills. Earn
            money by vetting candidates or stand out with
            community-verified skills on your CV2.0</p>
            
            <div class="hero-buttons">
                <a href="#for-vetters" class="hero-btn btn-primary">I Want to Become a Vetter</a>
                <a href="#for-candidates" class="hero-btn btn-secondary">I Want to Verify My Skills</a>
            </div>
            
            <form class="email-form">
                <input type="email" class="email-input" placeholder="Your email address" required>
                <button type="submit" class="submit-button">Join the Revolution</button>
            </form>
        </div>
    </section>

    <!-- Two Sides Section -->
    <section class="two-sides">
        <div class="container">
            <h2>Decentralized vetting platform</h2>
            <div class="sides-container">
                <div class="side-card vetter-side">
                    <h3><i class="fas fa-money-bill-wave"></i> For world's best senior developers</h3>
                    <p>Monetize your expertise and gain reputation in the
                    SkillVerdict community by verifying other developers'
                    skills.</p>
                    <br />
                    <ul class="benefit-list">
                        <li>Earn money reviewing code and conducting technical interviews</li>
                        <li>Work flexibly on your own schedule from anywhere,
                        no contracts, no dedicated work time</li>
                        <li>Build your reputation as an industry expert, and
                        stand out in the community</li>
                        <li>Get paid for each developer you help and verification you complete</li>
                        <li>Receive bonuses for high-quality feedback and reputation</li>
                        <li>Connect with very talented developers worldwide</li>
                    </ul>
                </div>
                
                <div class="side-card candidate-side">
                    <h3><i class="fas fa-award"></i>For developers who want CV2.0</h3>
                    <p>Get your skills verified by industry experts and boost
                    your career with a next-generation CV2.0.</p>
                    <br />
                    <ul class="benefit-list">
                        <li>Stand out with verified skill badges on your profile</li>
                        <li>Showcase credentials validated by world's best
                        senior developers from one of the world's largest companies</li>
                        <li>Increase your chances of getting hired 70% faster</li>
                        <li>Export a trusted, QR-verified digital CV2.0 - the modern alternative to traditional resumes</li>
                        <li>Get discovered by top companies seeking pre-vetted talent</li>
                        <li>Join a community that values real skills rather than
                        years on your resume</li>
                        <li>Start gaining reputation by helping other developers</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- For Vetters Section -->
    <section class="for-vetters" id="for-vetters">
        <div class="container">
            <h2>Why Senior Developers Love Vetting on SkillVerdict</h2>
            <div class="vetter-cards">
                <div class="vetter-card">
                    <i class="fas fa-dollar-sign"></i>
                    <h3>Earn Money</h3>
                    <p>Get paid for every verification you complete, with bonuses for quality and efficiency.</p>
                </div>
                
                <div class="vetter-card">
                    <i class="fas fa-clock"></i>
                    <h3>Flexible Schedule</h3>
                    <p>Work whenever you want and how much you want, from anywhere. No fixed hours, no
                    location requirements, no contracts</p>
                </div>
                
                <div class="vetter-card">
                    <i class="fas fa-network-wired"></i>
                    <h3>Build Your Network</h3>
                    <p>Connect with talented developers and expand your
                    professional network, with real professions in their field</p>
                </div>
                
                <div class="vetter-card">
                    <i class="fas fa-medal"></i>
                    <h3>Enhance Your Reputation</h3>
                    <p>Gain recognition as an industry expert and build your personal brand.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="process" id="how-it-works">
        <div class="container">
            <h2>How SkillVerdict Works</h2>
            
            <div class="process-tabs">
                <div class="process-tab active" data-tab="vetter">For Vetters</div>
                <div class="process-tab" data-tab="candidate">For Candidates</div>
            </div>
            
            <div class="process-content active" id="vetter-process">
                <div class="steps">
                    <div class="step">
                        <div class="icon">📋</div>
                        <h3>1. Apply as a Vetter</h3>
                        <p>Submit your CV and the vetter application</p>
                    </div>
                     <div class="step">
                        <div class="icon">🧪</div>
                        <h3>2. Vetter assessment</h3>
                        <p>Your will be interviewed and tested if you are a
                        match to become a vetter, after 2-3 days you will get a response</p>
                    </div>
                    <div class="step">
                        <div class="icon">💬</div>
                        <h3>3. Start working as a vetter</h3>
                        <p>You gain access to our AI assisted dashboard where you can
                        review candidates' work and lead technical discussions to validate
                        candidates' skills and knowledge.</p>
                    </div>
                    <div class="step">
                        <div class="icon">💰</div>
                        <h3>4. Get Paid</h3>
                        <p>Receive payment for each completed verification, with
                        bonuses for quality. Each completed candidate interview
                        is rated by the candidate for reputation</p>
                    </div>
                </div>
            </div>
            
            <div class="process-content" id="candidate-process">
                <div class="steps">
                    <div class="step">
                        <div class="icon">📝</div>
                        <h3>1. Create Profile</h3>
                        <p>Sign up, upload your CV and select the skills you want to verify.</p>
                    </div>
                    <div class="step">
                        <div class="icon">🧠</div>
                        <h3>2. Complete a short Take-Home Project for the
                        chosen skill</h3>
                        <p>Submit a take-home project that demonstrates your
                        skills. We want to flush out your most valuable skill/s
                        on the surface not drown them in "reversing a binary tree"</p>
                    </div>
                    <div class="step">
                        <div class="icon">💬</div>
                        <h3>3. Technical Discussion</h3>
                        <p>Discuss your solution with a world expert senior
                        developer vetter and get feedback and an assessment</p>
                    </div>
                    <div class="step">
                        <div class="icon">✅</div>
                        <h3>4. Get Verified</h3>
                        <p>After the technical assesment is done, you will get
                        your verified badge and get a chance to rate Receive your verified skill badge on your digital
                        CV2.0</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="testimonial">
        <div class="container">
            <h2>What Our Community Says</h2>
            
            <div class="testimonials-container">
                <div class="testimonial-card">
                    <div class="testimonial-text">
                        As a senior developer at a FAANG company, SkillVerdict lets me monetize my expertise on my own schedule. I've earned over $8,000 in the past three months while helping other developers advance their careers.
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">MJ</div>
                        <div class="author-info">
                            <h4>Michael Johnson</h4>
                            <p>Senior Engineer at Google, Top Vetter</p>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <div class="testimonial-text">
                        Getting my React skills verified by a Netflix senior engineer was a game-changer. I doubled my freelance rate and landed contracts with two startups that specifically mentioned my SkillVerdict badges and CV2.0 profile.
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">SP</div>
                        <div class="author-info">
                            <h4>Sarah Peterson</h4>
                            <p>Frontend Developer, SkillVerdict User</p>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <div class="testimonial-text">
                        As a hiring manager, SkillVerdict has cut our technical screening time by 70%. We now prioritize candidates with verified skills and CV2.0 profiles, which has dramatically improved our hiring success rate.
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">DR</div>
                        <div class="author-info">
                            <h4>David Rodriguez</h4>
                            <p>CTO at TechStartup, Hiring Partner</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta" id="waitlist">
        <div class="container">
            <h2>Ready to Monetize Your Skills or Get Verified?</h2>
            <p>Join our waitlist to be among the first to access the SkillVerdict platform and get your CV2.0 profile. Early members will get special perks and pricing.</p>
            
            <form class="email-form">
                <input type="email" class="email-input" placeholder="Enter your email to get early access" required>
                <button type="submit" class="submit-button">Secure My Spot</button>
            </form>
            
            <div style="margin-top: 30px; display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <a href="#for-vetters" class="cta-button">I'm a Senior Developer</a>
                <a href="#for-candidates" class="cta-button">I Want to Verify My Skills</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>© 2024 SkillVerdict. All rights reserved.</p>
            <p><a href="#">Privacy Policy</a> | <a href="#">Terms of Service</a></p>
            <div class="social-links">
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-linkedin"></i></a>
                <a href="#"><i class="fab fa-github"></i></a>
            </div>
        </div>
    </footer>

    <script>
        // Simple form submission handling
        document.querySelectorAll('.email-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = this.querySelector('input[type="email"]').value;
                if (email) {
                    alert(`Thank you! We've added ${email} to our waitlist. You'll be among the first to know when we launch!`);
                    this.querySelector('input[type="email"]').value = '';
                }
            });
        });
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Process tabs functionality
        const processTabs = document.querySelectorAll('.process-tab');
        const processContents = document.querySelectorAll('.process-content');
        
        processTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');
                
                // Update active tab
                processTabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // Show relevant content
                processContents.forEach(content => {
                    content.classList.remove('active');
                    if (content.id === `${tabId}-process`) {
                        content.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>