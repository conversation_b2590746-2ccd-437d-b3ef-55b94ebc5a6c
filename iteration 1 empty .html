<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkillVerdict | Community-Powered Skill Verification</title>
    <!-- Styles would go here -->
</head>
<body>

    <!-- Navbar -->
    <nav>
        <div class="logo">SkillVerdict</div>
        <div class="nav-links">
            <a href="#home">Home</a>
            <a href="#how-it-works">How It Works</a>
            <a href="#waitlist" class="cta-button">Join Waitlist</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <h1>Your Skills, <strong>Community-Verified.</strong></h1>
        <p>Stand out in the age of AI. Get your technical skills peer-reviewed by expert developers and build a trusted, digital CV that hiring managers believe in.</p>
        <form class="email-form">
            <input type="email" placeholder="Your email address" required>
            <button type="submit">Join Waitlist</button>
        </form>
    </section>

    <!-- The Problem Section -->
    <section class="problem">
        <h2>The Trust Gap in Tech Hiring</h2>
        <div class="pain-points">
            <div class="point">
                <h3>🤔 Self-Reported Skills</h3>
                <p>Anyone can claim to be a "React expert" on their resume. Employers are skeptical, and your genuine experience gets lost in the noise.</p>
            </div>
            <div class="point">
                <h3>⏳ Lengthy Vetting Processes</h3>
                <p>Companies spend thousands and waste weeks on technical screenings, while developers hate another grueling LeetCode marathon.</p>
            </div>
            <div class="point">
                <h3>🌐 No Portable Proof</h3>
                <p>Platform-specific badges and ratings are locked in. You lack
                a single, verifiable credential you can take anywhere to prove
                your worth. It is like your digital ID of your skills, your
                skills are your own verified tested and proven.</p>
            </div>
        </div>
    </section>

    <!-- The Solution Section -->
    <section class="solution">
        <h2>Introducing SkillVerdict: Credibility, Decentralized.</h2>
        <div class="benefits">
            <div class="benefit">
                <h3>🚀 Get Verified by Experts</h3>
                <p>Your skills are assessed and validated by senior developers from top companies, not just an automated test. Their reputation backs yours.</p>
            </div>
            <div class="benefit">
                <h3>📄 Your Verified Digital CV</h3>
                <p>Showcase a sleek, online CV with QR-verifiable badges like <em>"React v18 - Validated by a Senior Google Dev"</em>. Export it as a trusted PDF for any job application.</p>
            </div>
            <div class="benefit">
                <h3>💸 Earn &amp; Learn</h3>
                <p>Junior devs gain credibility. Senior devs earn money and
                build their reputation by flexibly vetting others. It's a
                community that lifts everyone up. Juniors rate their vetters and
                vetters rate their juniors so they can start building their
                reputation.</p>
            </div>
             <div class="benefit">
                <h3>💸 No pay to win!</h3>
                <p>Reputation can only be earned and gained in the communoty</p>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="process">
        <h2>How It Works in 3 Simple Steps</h2>
        <div class="steps">
            <div class="step">
                <div class="icon">📝</div>
                <h3>1. Build Your Profile</h3>
                <p>Sign up, upload your CV, and list the skills you want to get verified.</p>
            </div>
            <div class="step">
                <div class="icon">🧪</div>
                <h3>2. Get Vetted by Peers</h3>
                <p>Complete a take-home project and a discussion with our community of expert vetters.</p>
            </div>
            <div class="step">
                <div class="icon">✅</div>
                <h3>3. Unlock Opportunities</h3>
                <p>Add verified badges to your profile, share your CV with confidence, and get discovered by companies.</p>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="testimonial">
        <blockquote>
            "As a hiring manager, I spend 50% of my time sifting through unvetted resumes. A platform where developers come pre-verified by a community I trust would cut our hiring time in half and save us a fortune. I can't wait for this."
        </blockquote>
        <cite>- Sarah K., Senior Engineering Lead at a FinTech Startup (Potential User)</cite>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta">
        <h2>Be Among the First to Build Trusted Credibility</h2>
        <p>Join our waitlist for exclusive early access. Let's take back skill verification from algorithms and give it to the community.</p>
        <form class="email-form">
            <input type="email" placeholder="Your email address" required>
            <button type="submit">Secure Your Spot</button>
        </form>
    </section>

    <!-- Footer -->
    <footer>
        <p>© 2023 SkillVerdict. All rights reserved.</p>
        <p><a href="/privacy-policy">Privacy Policy</a></p>
        <div class="social-links">
            <!-- Social Icons would go here -->
        </div>
    </footer>

</body>
</html>