<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkillVerdict | CV 2.0 - Your Verified Digital Identity</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary: #f59e0b;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --success: #10b981;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            color: var(--dark);
            background-color: var(--light);
            line-height: 1.6;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header & Navigation */
        header {
            background-color: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 100;
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            display: flex;
            align-items: center;
        }
        
        .logo i {
            margin-right: 8px;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
        }
        
        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: var(--primary);
        }
        
        .cta-button {
            background-color: var(--primary);
            color: white;
            padding: 10px 20px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: background-color 0.3s;
        }
        
        .cta-button:hover {
            background-color: var(--primary-dark);
        }
        
        /* Hero Section */
        .hero {
            padding: 150px 0 100px;
            text-align: center;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            line-height: 1.2;
        }
        
        .hero p {
            font-size: 1.5rem;
            color: var(--gray);
            max-width: 800px;
            margin: 0 auto 40px;
        }
        
        .email-form {
            display: flex;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .email-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #ddd;
            border-radius: 50px 0 0 50px;
            font-size: 1rem;
            outline: none;
        }
        
        .submit-button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 0 50px 50px 0;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-button:hover {
            background-color: var(--primary-dark);
        }
        
        /* Sections */
        section {
            padding: 80px 0;
        }
        
        section h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 50px;
            color: var(--dark);
        }
        
        /* Problem Section */
        .problem {
            background-color: white;
        }
        
        .pain-points {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .point {
            background-color: var(--light);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            text-align: center;
        }
        
        .point h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        /* Solution Section */
        .solution {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }
        
        .benefits {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .benefit {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            text-align: center;
        }
        
        .benefit h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        /* How It Works */
        .process {
            background-color: white;
        }
        
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .step {
            text-align: center;
            padding: 30px 20px;
        }
        
        .step .icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--primary);
        }
        
        .step h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        /* Testimonial */
        .testimonial {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            text-align: center;
        }
        
        blockquote {
            font-size: 1.5rem;
            font-style: italic;
            max-width: 800px;
            margin: 0 auto 30px;
            padding: 0 20px;
            color: var(--dark);
        }
        
        cite {
            font-weight: 600;
            color: var(--primary);
        }
        
        /* Final CTA */
        .final-cta {
            text-align: center;
            padding: 80px 0;
            background-color: white;
        }
        
        .final-cta h2 {
            margin-bottom: 20px;
        }
        
        .final-cta p {
            font-size: 1.2rem;
            color: var(--gray);
            max-width: 600px;
            margin: 0 auto 40px;
        }
        
        /* Footer */
        footer {
            background-color: var(--dark);
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        footer a {
            color: #93c5fd;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        footer a:hover {
            color: white;
        }
        
        .social-links {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .social-links a {
            color: white;
            font-size: 1.5rem;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.2rem;
            }
            
            .email-form {
                flex-direction: column;
            }
            
            .email-input {
                border-radius: 50px;
                margin-bottom: 10px;
            }
            
            .submit-button {
                border-radius: 50px;
            }
            
            .nav-links {
                display: none;
            }
            
            .mobile-menu-btn {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- Header & Navigation -->
    <header>
        <div class="container">
            <nav>
                <div class="logo">
                    <i class="fas fa-certificate"></i>
                    SkillVerdict
                </div>
                <div class="nav-links">
                    <a href="#home">Home</a>
                    <a href="#how-it-works">How It Works</a>
                    <a href="#waitlist" class="cta-button">Join Waitlist</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <h1>Your Resume is Obsolete. Meet Your <strong>Verified Digital Identity.</strong></h1>
            <p>Stop just listing your skills. Start proving them. SkillVerdict is your community-verified, AI-powered CV 2.0—a living credential that hiring managers trust on sight.</p>
            <form class="email-form">
                <input type="email" class="email-input" placeholder="Your email address" required>
                <button type="submit" class="submit-button">Join the Revolution</button>
            </form>
        </div>
    </section>

    <!-- The Problem Section -->
    <section class="problem">
        <div class="container">
            <h2>The Trust Gap in Tech Hiring</h2>
            <div class="pain-points">
                <div class="point">
                    <h3>🤔 The "Trust-Me" Trap</h3>
                    <p>Your resume is a list of claims. In a world flooded with AI-generated code and inflated titles, "10 years of experience" is just words. Your genuine talent is invisible.</p>
                </div>
                <div class="point">
                    <h3>⏳ The Vetting Black Hole</h3>
                    <p>Companies waste months and millions on flawed technical interviews. Developers waste energy on yet another algorithm puzzle. The system is broken for everyone.</p>
                </div>
                <div class="point">
                    <h3>🔒 Your Skills, Trapped</h3>
                    <p>Platform badges and LinkedIn endorsements are locked in silos. You deserve a portable, undeniable proof of skill that you own and control—your professional identity, verified.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- The Solution Section -->
    <section class="solution">
        <div class="container">
            <h2>Introducing SkillVerdict: CV 2.0 - Proof, Not Promises</h2>
            <div class="benefits">
                <div class="benefit">
                    <h3>🚀 Verification That Means Something</h3>
                    <p>Forget automated tests. Your skills are validated in real-world scenarios by senior engineers from companies like Google and Netflix. Their reputation becomes your credibility.</p>
                </div>
                <div class="benefit">
                    <h3>📄 A Living, Breathing Credential</h3>
                    <p>This isn't a PDF; it's your Verified Digital Identity. Showcase dynamic, QR-powered badges like "React v18 - Validated by a Netflix Sr. Engineer" that prove your depth of knowledge.</p>
                </div>
                <div class="benefit">
                    <h3>💎 Build Your Reputation Economy</h3>
                    <p>Juniors gain instant credibility. Seniors monetize expertise and shape the next generation. This is a meritocracy where your skill is your currency.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="process" id="how-it-works">
        <div class="container">
            <h2>How It Works: Your Path to a Verified Identity</h2>
            <div class="steps">
                <div class="step">
                    <div class="icon">📝</div>
                    <h3>1. Claim Your Skills</h3>
                    <p>Build your digital profile and declare the competencies you want to verify. This is the foundation of your new CV.</p>
                </div>
                <div class="step">
                    <div class="icon">🧠</div>
                    <h3>2. Undergo Peer Review</h3>
                    <p>Demonstrate your skills through a practical project and a technical discussion with our curated community of expert developers.</p>
                </div>
                <div class="step">
                    <div class="icon">✅</div>
                    <h3>3. Showcase Your Verified CV</h3>
                    <p>Activate your verified badges. Share your unique SkillVerdict profile URL or download a secured PDF. Get discovered by companies.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="testimonial">
        <div class="container">
            <blockquote>
                "As a hiring manager, I don't have time for guesswork. The concept of a 'living CV' where a developer's skills are pre-verified by a trusted community is revolutionary. It would make our hiring process 60% faster and finally give us confidence in our offers."
            </blockquote>
            <cite>- Maria L., Director of Engineering at a Series B Startup</cite>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta" id="waitlist">
        <div class="container">
            <h2>Secure Your Spot in the Future of Hiring</h2>
            <p>Join the waitlist to be among the first to build your Verified Digital Identity. Get early access, exclusive pricing, and help us shape CV 2.0.</p>
            <form class="email-form">
                <input type="email" class="email-input" placeholder="Enter your email to get early access" required>
                <button type="submit" class="submit-button">Secure My Spot</button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>© 2024 SkillVerdict. All rights reserved.</p>
            <p><a href="#">Privacy Policy</a> | <a href="#">Terms of Service</a></p>
            <div class="social-links">
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-linkedin"></i></a>
                <a href="#"><i class="fab fa-github"></i></a>
            </div>
        </div>
    </footer>

    <script>
        // Simple form submission handling
        document.querySelectorAll('.email-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = this.querySelector('input[type="email"]').value;
                if (email) {
                    alert(`Thank you! We've added ${email} to our waitlist. You'll be among the first to know when we launch!`);
                    this.querySelector('input[type="email"]').value = '';
                }
            });
        });
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>